<script lang="ts" setup>
import {
  Modal as hModal,
  message
} from 'ant-design-vue';
import { computed, ref, watch, onMounted, nextTick } from "vue";
import type { Ref } from "vue";
import {
  IAnnouncementNotice
} from '@haierbusiness-front/common-libs';
import { announcementNoticeApi } from '@haierbusiness-front/apis';

interface Props {
  show: boolean;
  data: IAnnouncementNotice | undefined;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const confirmLoading = ref(false);
const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);


// 确认处理
const handleOk = async () => {
  emit('ok')
}
const notice = ref()
const noticeContent = ref()

// 监听props变化，当编辑数据变化时更新表单，添加immediate属性确保初始化时执行
watch(
  () => props.data,
  (newValue) => {
    console.log('props.data变化:', newValue);
    // 确保编辑时数据正确回显
    if (newValue) {
      // console.log('编辑数据回显 - 原始数据:', newValue);
      // console.log('编辑数据回显 - 公告内容:', newValue.informContent);
      notice.value = newValue
      nextTick(() => {
        noticeContent.value.innerHTML = newValue.informContent
      })

    } else {

    }
  },
  { immediate: true, deep: true }
);

// 组件挂载时检查初始数据
onMounted(() => {

});



</script>

<template>
  <h-modal :open="visible" :width="1056" class="mice-bid-instruction-modal" @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading">
    <template #footer>
      <a-button type="primary" @click="handleOk()">我已知晓</a-button>
    </template>
    <div class="notice-container">
      <div class="notice-content">
        <h2 style="text-align: center;">{{ notice?.title }}</h2>
        <div class="notice-date">
          <p>发布时间：{{ notice?.gmtCreate }}</p>
          <p>发布人：{{ notice?.createName }}</p>
        </div>
        <div :ref="el => noticeContent = el" class="noticeContent"></div>
      </div>
    </div>
  </h-modal>
</template>

<style lang="less" scoped>
.mice-bid-instruction-modal {
  .ant-modal-content {
    background: linear-gradient(181deg, #e4efff 0%, #ffffff 200px, #ffffff 100%) !important;
    border-radius: 16px !important;
    padding: 20px 64px;

    .ant-modal-header {
      background: transparent;

      .ant-modal-title {
        text-align: center;
        font-weight: 500;
        font-size: 24px;
        color: #1d2129;
      }
    }
  }

  .ant-modal-footer {
    margin-top: 28px;
  }
}

.notice-date {
  display: flex;
  justify-content: center;
  margin: 5px 0;

  p {
    margin-bottom: 0;
    margin-right: 25px;
  }
}

.notice-container {
  overflow: hidden;
  overflow-y: auto;
}


::v-deep(.noticeContent) {
  img {
    width: 100px;
    height: 100px;
  }

  ul,
  ol,
  li {
    list-style: circle !important;
  }
}
</style>