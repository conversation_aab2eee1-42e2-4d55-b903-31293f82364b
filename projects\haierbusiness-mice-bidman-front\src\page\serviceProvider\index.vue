<!-- 服务商管理端列表 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  InputNumber as hInputNumber,
  Upload as hUpload,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  message,
} from 'ant-design-vue';
import { UploadOutlined, SearchOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { serviceProviderApi, fileApi, miceBidManServiceProviderApi, } from '@haierbusiness-front/apis';
import {
  TrialStateEnum,
  ServiceProviderStateEnum,
  ServiceProviderStateMap,
  UploadFile,
  TrialStateMap,
  ServiceProviderFilter,
  MerchantTypeEnum,
  getMerchantTypeOptions,
  MerchantTypeMap
} from '@haierbusiness-front/common-libs';
import { computed, ref, onMounted, nextTick, reactive, h, watch } from 'vue';
import { usePagination, } from 'vue-request';
import router from '../../router';
import ColumnFilter from '@haierbusiness-front/components/mice/search/ColumnFilter.vue';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
import type { MenuItemType, MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
import dayjs, { Dayjs } from 'dayjs';


const currentRouter = ref()

const columns: ColumnType[] = [
  {
    title: '商户企业编码',
    dataIndex: 'merchantCode',
    width: '250px',
    align: 'center',
  },
  {
    title: '商户名称',
    dataIndex: 'merchantName',
    width: '300px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商类型',
    dataIndex: 'merchantType',
    width: '120px',
    align: 'center',
    customRender: ({ text }: { text: number }) => MerchantTypeMap[text as MerchantTypeEnum] || '-',
  },
  {
    title: '商户简介',
    dataIndex: 'merchantDesc',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  // {
  //   title: '保证金',
  //   dataIndex: 'earnestMoney',
  //   width: '160px',
  //   align: 'center',
  //   customRender: ({ text }) => text != null ? `${text}元` : '',
  // },
  {
    title: '评分',
    dataIndex: 'score',
    width: '80px',
    align: 'center',
  },
  {
    title: '是否转正',
    dataIndex: 'trialState',
    width: '100px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }: { text: number }) => TrialStateMap[text as TrialStateEnum] || '-',
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '100px',
    align: 'center',
    customRender: ({ text }: { text: number }) => ServiceProviderStateMap[text as ServiceProviderStateEnum] || '-',
  },
  {
    title: '引入时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,

  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
];

const searchParam = ref<ServiceProviderFilter>({
  merchantName: '',
  merchantType: undefined,
  state: undefined,
  begin: undefined,
  end: undefined
})
const {
  data,
  run: listApiRun,
  loading,
} = usePagination(serviceProviderApi.list);

const reset = () => {
  console.log('重置按钮被点击');
  // 先重置搜索参数
  searchParam.value = {}
  gmtCreate.value = undefined
  console.log('重置后的搜索参数:', searchParam.value);

  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10
    };
    console.log('重置后调用接口参数:', params);
    listApiRun(params);
  });
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {

  let params = {
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  };

  console.log('最终查询参数:', params);
  listApiRun(params);
};

// 创建时间范围
const gmtCreate = ref<[Dayjs, Dayjs]>()
watch(() => gmtCreate.value, (n: any) => {
  if (n) {
    searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.startTime = undefined
    searchParam.value.endTime = undefined
  }
});

const handleCreate = () => {
  currentRouter.value.push({
    path: '/bidman/serviceProvider/add',
  })
}

const handleDetail = (record: any) => {
  currentRouter.value.push({
    path: '/bidman/serviceProvider/serviceDetail',
    query: {
      id: record.id
    }
  })
}

const handleAudit = (record: any) => {
  currentRouter.value.push({
    path: '/bidman/serviceProvider/serviceProviderAudit',
    query: {
      id: record.id
    }
  })
}

// 弹框相关
const modalVisible = ref(false);
const modalType = ref(''); // 'disable' 或 'trial' 或 'enable'
const reason = ref('');
const fileList = ref<UploadFile[]>([]);
const meetingCount = ref<number | undefined>(undefined);
const freezeTime = ref<number| undefined>(undefined);
const currentRecord = ref<any>(null);
const uploadLoading = ref(false);
const formRef = ref();

// 表单校验规则
const formRules = computed(() => {
  return {
    reason: [
      {
        required: true,
        message: `请输入${modalType.value === 'disable' ? '停用' : modalType.value === 'enable' ? '启用' : ''}理由`,
        trigger: 'blur',
        type: 'string',
      }
    ],
    freezeTime: [
      {
        required: modalType.value === 'disable',
        message: '请输入停用时长',
        trigger: 'blur',
        type: 'number',
      }
    ],
    meetingCount: [
      {
        required: modalType.value === 'trial',
        type: 'number',
        message: '请输入转正还需承接会议数量',
        trigger: 'blur',
      }
    ]
  };
});

// 打开弹框
const handleOpenModal = (record: any, type: string) => {
  console.log('打开弹框', record, type);
  currentRecord.value = record;
  modalType.value = type;
  modalVisible.value = true;
  // 重置表单
  reason.value = '';
  meetingCount.value = undefined;
  freezeTime.value = undefined;
  fileList.value = [];

  // 在modalType更新后重置表单校验
  nextTick(() => {
    formRef.value && formRef.value.clearValidate();
  });
};

// 监听modalType变化，确保弹框内容和校验规则同步更新
watch(() => modalType.value, () => {
  nextTick(() => {
    // 如果表单已经渲染，则重新进行校验规则的应用
    if (formRef.value && modalVisible.value) {
      formRef.value.clearValidate();
    }
  });
});

// 上传附件
// @ts-ignore - 兼容vite环境变量
const baseUrl = import.meta.env.VITE_BUSINESS_URL || '';
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi.upload(formData)
    .then((it) => {
      // 直接设置file对象的属性
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 审批流程相关
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;
const approvalModalShow = ref(false);
const approveCode = ref<string>(''); // 审批流程Code

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单校验
    await formRef.value.validate();

    if (modalType.value === 'disable' || modalType.value === 'enable') {
      const params = {
        merchantId: currentRecord.value.id,
        reason: reason.value,
        attachmentFile: fileList.value.map(file => file.filePath),
        state: modalType.value === 'disable' ? 1 : 0,
        freezeTime: modalType.value === 'disable' && freezeTime.value ? freezeTime.value : undefined,
      };
      console.log(`${modalType.value === 'disable' ? '停用' : '启用'}服务商传递参数:`, params);
      await miceBidManServiceProviderApi.disable(params)
      .then((result)=>{
        approveCode.value = result;
          console.log(approveCode.value, 'approveCode.value');

          approvalModalShow.value = true;
      })
    } else {
      await miceBidManServiceProviderApi.setTrial({
        merchantId: currentRecord.value.id,
        trialEndMiceNum: meetingCount.value,
        reason: reason.value,
        attachmentFile: fileList.value.map(file => file.filePath)
      }).then((result)=>{
        approveCode.value = result;
          console.log(approveCode.value, 'approveCode.value');

          approvalModalShow.value = true;
      })
    }

    message.success(
      modalType.value === 'disable' ? '冻结申请已提交' :modalType.value === 'enable' ? '解冻申请已提交' : '设置成功'
    );
    modalVisible.value = false;
    // 刷新列表
    listApiRun({
      ...searchParam.value,
      pageNum: 1,
      pageSize: 10
    });
  } catch (error) {
    console.error(error);
    message.error(
      modalType.value === 'disable' ? '停用失败' :
        modalType.value === 'enable' ? '启用失败' : '设置失败'
    );
  }
};

// 移除文件
const handleRemove = (file: any) => {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
};

// 添加搜索处理函数
const handleSearch = () => {
  // 使用已定义的ServiceProviderFilter类型
  const params: ServiceProviderFilter = {
    pageNum: 1,
    pageSize: 10,
    ...searchParam.value
  };

  // 过滤掉undefined和空字符串的参数
  Object.keys(params).forEach(key => {
    if (params[key as keyof ServiceProviderFilter] === undefined || params[key as keyof ServiceProviderFilter] === '') {
      delete params[key as keyof ServiceProviderFilter];
    }
  });

  console.log('搜索参数:', params);
  listApiRun(params);
};

// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string;
  switch (key) {
    case 'audit':
      handleAudit(record);
      break;
    case 'disable':
      handleOpenModal(record, 'disable');
      break;
    case 'trial':
      handleOpenModal(record, 'trial');
      break;
    case 'enable':
      handleOpenModal(record, 'enable');
      break;
    case 'edit':
      handleEdit(record);
      break;
    default:
      break;
  }
};

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = [
    {
      key: 'audit',
      label: '考核',
    },
    {
      key: 'edit',
      label: '编辑',
    }
  ];

  if (record.state === 0) {
    options.push(
      {
        key: 'disable',
        label: '冻结',
      },
      {
        key: 'trial',
        label: '设置试用期',
      }
    );
  }

  if (record.state === 1) {
    options.push({
      key: 'enable',
      label: '解冻',
    });
  }

  return options;
};

const handleEdit = (record: any) => {
  currentRouter.value.push({
    path: '/bidman/serviceProvider/add',
    query: {
      id: record.id
    }
  })
}

onMounted(async () => {
  currentRouter.value = await router
  listApiRun({
    pageNum: 1,
    pageSize: 10
  })
})

</script>

<template>
  <div class="page-container">
    <h-row class="row-container">
      <h-col :span="24" class="col-margin">
        <h-row class="search-row">
          <h-col :span="2" class="label-right modify">
            <label for="merchantName">商户企业名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="merchantName" v-model:value="searchParam.merchantName" placeholder="请输入" allow-clear
              :maxlength="200" />
          </h-col>

          <h-col :span="2" class="label-right">
            <label for="merchantType">商户类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select id="merchantType" v-model:value="searchParam.merchantType" placeholder="请选择商户类型"
              class="full-width" allow-clear>
              <h-select-option v-for="option in getMerchantTypeOptions()" :key="option.value" :value="option.value">
                {{ option.label }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" class="label-right">
            <label for="state">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select id="state" v-model:value="searchParam.state" style="width: 100%;" placeholder="请选择状态" allow-clear>
              <h-select-option v-for="(label, value) in ServiceProviderStateMap" :key="value" :value="Number(value)">
                {{ label }}
              </h-select-option>
            </h-select>
          </h-col>
          <!-- <h-col :span="2" class="label-right">
            <label for="state">保证金：</label>
          </h-col>
          <h-col :span="4" style="flex: 0 0 14.6%;max-width: 14.6%;">
            <h-input v-model:value="searchParam.earnestMoney" placeholder="请输入保证金" allow-clear :maxlength="200" /> -->
          <!-- </h-col> -->
          <h-col :span="2" class="label-right">
            <label for="trialState">是否转正：</label>
          </h-col>
          <h-col :span="4" style="flex: 0 0 14.6%;max-width: 14.6%;">
            <h-select id="trialState" v-model:value="searchParam.trialState" style="width: 100%;" placeholder="请选择是否转正" allow-clear>
              <h-select-option :value="1">正式</h-select-option>
              <h-select-option :value="0">试用期</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row class="search-row">
          <h-col :span="2" class="label-right modify">
            <label for="merchantCode">商户企业编码：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="merchantCode" v-model:value="searchParam.merchantCode" placeholder="请输入商户企业编码" allow-clear
              :maxlength="200" />
          </h-col>
          <h-col :span="2" class="label-right">
            <label for="score">评分：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.score" placeholder="请输入评分" allow-clear :maxlength="200" />
          </h-col>
          <h-col :span="2" class="label-right">
            <label for="gmtCreate">引入时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker id="gmtCreate" v-model:value="gmtCreate" value-format="YYYY-MM-DD" style="width: 100%"
              allow-clear />
          </h-col>

        </h-row>
        <h-row class="button-row">
          <h-col :span="24" class="text-right">
            <h-button class="margin-right" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleSearch">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row class="button-row">
          <h-col :span="12" class="text-left">
            <h-button type="primary" @click="handleCreate">
              <PlusOutlined /> 引入
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" size="small" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading"
          @change="(pagination) => handleTableChange({ current: pagination.current || 1, pageSize: pagination.pageSize || 10 })">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <div class="operator-buttons">
                <h-button type="link" @click="handleDetail(record)">详情</h-button>
                <Actions :menu-options="getMenuOptions(record)" :on-menu-click="(e) => handleMenuClick(record, e)">
                </Actions>
              </div>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <!-- 通用弹框 -->
    <h-modal :open="modalVisible" :title="modalType === 'disable' ? '服务商冻结' : modalType === 'enable' ? '服务商解冻' : '设置试用'"
      @cancel="modalVisible = false" @ok="handleSubmit" :confirmLoading="uploadLoading">
      <h-form ref="formRef" :model="{ reason, meetingCount, freezeTime }" :rules="formRules" layout="vertical"
        :hide-required-mark="true">
        <div class="form-container">
          <template v-if="modalType === 'trial'">
            <h-form-item name="meetingCount" label="转正还需承接会议数量：">
              <div class="input-with-unit">
                <h-input-number v-model:value="meetingCount" placeholder="请输入数量" :min="1" :precision="0"
                  style="width: calc(100% - 40px)" type="number" />
                <span class="unit">场</span>
              </div>
            </h-form-item>
          </template>

          <h-form-item name="reason"
            :label="`${modalType === 'disable' ? '冻结理由' : modalType === 'enable' ? '解冻理由' : '理由'}：`">
            <h-input v-model:value="reason"
              :placeholder="`请输入${modalType === 'disable' ? '停用' : modalType === 'enable' ? '启用' : ''}理由`" :rows="4"
              :maxlength="200" show-count />
          </h-form-item>

          <template v-if="modalType === 'disable'">
            <h-form-item name="freezeTime" label="冻结时长：">
              <h-input-number v-model:value="freezeTime" placeholder="请输入停用时长" :min="1" :precision="0" addonAfter="天"
                style="width: 100%" type="number"></h-input-number>
            </h-form-item>
          </template>

          <div class="form-item">
            <div class="label">见证性材料：</div>
            <h-upload :file-list="fileList" :customRequest="uploadRequest" :multiple="true" :maxCount="5"
              @change="(info) => { fileList = info.fileList }" @remove="handleRemove"
              accept=".pdf,.doc,.docx,.jpg,.png,.jpeg,.xls,.xlsx">
              <h-button>
                <template #icon>
                  <upload-outlined />
                </template>
                上传文件
              </h-button>
            </h-upload>
            <div class="upload-tip">支持格式：PDF、Word、Excel、JPG、PNG，单个文件不超过10M</div>
          </div>
        </div>
      </h-form>
    </h-modal>
    <!-- 审批流程模态框 -->
    <h-modal v-model:open="approvalModalShow" title="已提交如下人员审批" width="80%" :keyboard="false" :maskClosable="false"
      :closable="false">
      <div>
        <iframe width="100%" :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" frameborder="0"></iframe>
      </div>
      <template #footer>
        <h-button @click="
          approvalModalShow = false;
        router.push('/bidman/serviceProvider/index');
        ">确定</h-button>
      </template>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.page-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

.label-right {
  margin-top: 6px;
}

.row-container {
  align-items: middle;
}

.col-margin {
  margin-bottom: 10px;
}

.search-row {
  align-items: middle;
  padding: 10px 10px 0px 10px;
}

.button-row {
  align-items: middle;
  padding: 10px 10px 0px 10px;
}

.label-right {
  text-align: right;
  padding-right: 10px;
}

.margin-right {
  margin-right: 10px;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.full-width {
  width: 100%;
}

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.form-container {
  .form-item {
    margin-bottom: 24px;

    .label {
      margin-bottom: 8px;
    }
  }
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}

.input-with-unit {
  display: flex;
  align-items: center;

  .unit {
    margin-left: 8px;
    color: rgba(0, 0, 0, 0.85);
  }
}

.operator-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.operator-buttons :deep(.ant-btn) {
  padding: 0 4px;
  font-size: 14px;
}

.modify {
  display: block;
  flex: 0 0 10.333333%;
  max-width: 10.333333%;
}
</style>
