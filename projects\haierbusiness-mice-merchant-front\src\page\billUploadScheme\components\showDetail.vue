<script lang="ts" setup>
import { ref, inject } from 'vue';
import { useRoute } from 'vue-router';
import { resolveParam } from '@haierbusiness-front/utils';
import schemeDetails from '../../scheme/schemeDetails.vue';
import schemeChange from '../../scheme/schemeChange.vue';
import billUploadScheme from '@haierbusiness-front/components/billUploadScheme/billUploadschemeDetails.vue';

const route = useRoute();
const viewSelect = ref('demand');

// 获取路由参数
const routeParams = resolveParam(route.query.record as string);

// 目标项目的基础URL配置
const TARGET_PROJECT_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://your-production-domain.com' // 生产环境地址，需要替换为实际地址
  : 'http://localhost:3000'; // 开发环境地址，需要替换为实际端口

// 跳转到目标项目的详情页面
const jumpToTargetProject = () => {
  // 构造跳转参数
  const params = {
    miceId: routeParams.miceId,
    miceSchemeId: routeParams.miceSchemeId || '',
    schemeType: routeParams.schemeType || 'billUpload',
    platformType: 'merchant', // 固定为merchant，因为是从merchant项目跳转
    hotelLockId: routeParams.hotelLockId || '',
    miceSchemeDemandHotelLockId: routeParams.miceSchemeDemandHotelLockId || '',
    pdMainId: routeParams.pdMainId || '',
    pdVerId: routeParams.pdVerId || '',
    // 添加来源标识，便于目标项目识别
    source: 'merchant-front',
    timestamp: Date.now(), // 添加时间戳，避免缓存问题
  };

  // 过滤掉空值参数
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([_, value]) => value !== '' && value !== null && value !== undefined)
  );

  // 构造目标URL
  const targetUrl = `${TARGET_PROJECT_BASE_URL}/bill-upload-scheme/detail?${new URLSearchParams(filteredParams).toString()}`;

  console.log('跳转参数:', filteredParams);
  console.log('目标URL:', targetUrl);

  // 在新标签页打开
  window.open(targetUrl, '_blank');
};

// 处理返回操作
const handleGoBack = () => {
  // 可以根据实际需求调整返回逻辑
  window.history.back();
};
</script>
<template>
  <div class="container">
    <schemeDetails v-if="viewSelect === 'demand'"> </schemeDetails>
    <schemeChange v-else-if="viewSelect === 'scheme'"> </schemeChange>
    <billUploadScheme v-else-if="viewSelect === 'billUpload'"> </billUploadScheme>
    <div class="footer">
      <a-radio-group v-model:value="viewSelect">
        <a-radio-button value="demand">需求视图</a-radio-button>
        <a-radio-button value="scheme">方案视图</a-radio-button>
        <a-radio-button value="billUpload">账单视图</a-radio-button>
      </a-radio-group>
      <a-button type="primary">返回</a-button>
    </div>
  </div>
</template>
<style lang="less" scoped>
.container {
  padding-bottom: 40px;
  width: 100%;
  height: 100%;
  position: relative;
}
.footer {
  right: 0;
  background: #fff;
  z-index: 11;
  width: calc(100% - 250px);
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
}
</style>
